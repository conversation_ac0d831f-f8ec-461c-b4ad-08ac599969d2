<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户权限管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <script defer src="/static/js/alpine.min.js"></script>
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        [x-cloak] { display: none !important; }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }

        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        /* 开关样式 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #cbd5e1;
            transition: .3s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .3s;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        input:checked + .toggle-slider {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }

        /* 卡片悬停效果 */
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 排序表头样式 */
        .sort-header {
            background: none;
            border: none;
            padding: 0;
            font: inherit;
            color: inherit;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            transition: color 0.2s ease;
        }

        .sort-header:hover {
            color: #475569;
        }

        .sort-icon {
            transition: all 0.2s ease;
        }

        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
        }


        .custom-select-trigger {
            width: 100%;
            height: 48px;
            padding: 12px 40px 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }

        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 1000;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }

        .custom-select.active .custom-select-dropdown,
        .custom-select-dropdown.active {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .custom-select-options {
            max-height: 240px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        /* 滚动条样式 */
        .custom-select-options::-webkit-scrollbar {
            width: 4px;
        }

        .custom-select-options::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }

        .custom-select-options::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }

        /* 箭头旋转 */
        .rotate-180 {
            transform: translateY(-50%) rotate(180deg);
        }

        /* 确保下拉框在最顶层 */
        .custom-select.active {
            z-index: 1000;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div x-data="userPermissionManager()" x-init="init()" class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <!-- 操作按钮区域 -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex justify-end">
                <button @click="openAddModal()"
                        class="btn-primary h-12 px-6 text-white rounded-xl flex items-center gap-2 shadow-lg">
                    <i class="fas fa-plus"></i>
                    <span>添加权限</span>
                </button>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-6">
            <div class="bg-white/90 rounded-2xl shadow-sm border border-slate-200 p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-700 mb-2">搜索</label>
                        <input type="text" x-model="filters.search" @input="debounceSearch()"
                               placeholder="用户名、姓名或权限名称"
                               class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-slate-700 mb-2">权限名称</label>
                        <div class="custom-select" :class="{ 'active': permissionFilterDropdownOpen }">
                            <div class="custom-select-trigger" @click="permissionFilterDropdownOpen = !permissionFilterDropdownOpen">
                                <span class="custom-select-text" x-text="getPermissionFilterText()">全部权限</span>
                                <i class="fas fa-chevron-down custom-select-arrow" :class="{ 'rotate-180': permissionFilterDropdownOpen }"></i>
                            </div>
                            <div class="custom-select-dropdown" :class="{ 'active': permissionFilterDropdownOpen }">
                                <div class="custom-select-options">
                                    <div class="custom-select-option" @click="selectPermissionFilter(''); permissionFilterDropdownOpen = false"
                                         :class="{ 'selected': filters.permission_name === '' }">全部权限</div>
                                    <template x-for="option in (permissionOptions || [])" :key="option?.value || Math.random()">
                                        <div class="custom-select-option" @click="selectPermissionFilter(option?.value || ''); permissionFilterDropdownOpen = false"
                                             :class="{ 'selected': filters.permission_name === (option?.value || '') }" x-text="option?.label || ''"></div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-slate-700 mb-2">权限值</label>
                        <div class="custom-select" :class="{ 'active': permissionValueDropdownOpen }">
                            <div class="custom-select-trigger" @click="permissionValueDropdownOpen = !permissionValueDropdownOpen">
                                <span class="custom-select-text" x-text="getPermissionValueText()">全部</span>
                                <i class="fas fa-chevron-down custom-select-arrow" :class="{ 'rotate-180': permissionValueDropdownOpen }"></i>
                            </div>
                            <div class="custom-select-dropdown" :class="{ 'active': permissionValueDropdownOpen }">
                                <div class="custom-select-options">
                                    <div class="custom-select-option" @click="selectPermissionValue(''); permissionValueDropdownOpen = false"
                                         :class="{ 'selected': filters.permission_value === '' }">全部</div>
                                    <div class="custom-select-option" @click="selectPermissionValue('allow'); permissionValueDropdownOpen = false"
                                         :class="{ 'selected': filters.permission_value === 'allow' }">允许</div>
                                    <div class="custom-select-option" @click="selectPermissionValue('deny'); permissionValueDropdownOpen = false"
                                         :class="{ 'selected': filters.permission_value === 'deny' }">禁止</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-end">
                        <button @click="resetFilters()"
                                class="h-12 bg-slate-500 hover:bg-slate-600 text-white px-4 py-3 rounded-xl transition-all flex items-center justify-center gap-2">
                            <i class="fas fa-undo"></i>
                            <span>重置</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 权限列表 -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 overflow-hidden">
                <!-- 加载状态 -->
                <div x-show="loading" class="p-12 text-center">
                    <i class="fas fa-spinner fa-spin text-3xl text-blue-500 mb-4"></i>
                    <p class="text-slate-500">加载中...</p>
                </div>

                <!-- 权限表格 -->
                <div x-show="!loading" class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gradient-to-r from-slate-50 to-blue-50">
                            <tr>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">
                                    <button class="sort-header" @click="handleSort('name')" data-field="name">
                                        <span>用户信息</span>
                                        <div class="flex flex-col">
                                            <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                               :class="{'text-blue-800 opacity-100': sortField === 'name' && sortOrder === 'asc'}"></i>
                                            <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                               :class="{'text-blue-800 opacity-100': sortField === 'name' && sortOrder === 'desc'}"></i>
                                        </div>
                                    </button>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">
                                    <button class="sort-header" @click="handleSort('permission_name')" data-field="permission_name">
                                        <span>权限名称</span>
                                        <div class="flex flex-col">
                                            <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                               :class="{'text-blue-800 opacity-100': sortField === 'permission_name' && sortOrder === 'asc'}"></i>
                                            <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                               :class="{'text-blue-800 opacity-100': sortField === 'permission_name' && sortOrder === 'desc'}"></i>
                                        </div>
                                    </button>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">
                                    <button class="sort-header" @click="handleSort('permission_value')" data-field="permission_value">
                                        <span>权限值</span>
                                        <div class="flex flex-col">
                                            <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                               :class="{'text-blue-800 opacity-100': sortField === 'permission_value' && sortOrder === 'asc'}"></i>
                                            <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                               :class="{'text-blue-800 opacity-100': sortField === 'permission_value' && sortOrder === 'desc'}"></i>
                                        </div>
                                    </button>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">
                                    <button class="sort-header" @click="handleSort('is_enabled')" data-field="is_enabled">
                                        <span>状态</span>
                                        <div class="flex flex-col">
                                            <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                               :class="{'text-blue-800 opacity-100': sortField === 'is_enabled' && sortOrder === 'asc'}"></i>
                                            <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                               :class="{'text-blue-800 opacity-100': sortField === 'is_enabled' && sortOrder === 'desc'}"></i>
                                        </div>
                                    </button>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">
                                    <button class="sort-header" @click="handleSort('created_at')" data-field="created_at">
                                        <span>创建时间</span>
                                        <div class="flex flex-col">
                                            <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                               :class="{'text-blue-800 opacity-100': sortField === 'created_at' && sortOrder === 'asc'}"></i>
                                            <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                               :class="{'text-blue-800 opacity-100': sortField === 'created_at' && sortOrder === 'desc'}"></i>
                                        </div>
                                    </button>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-slate-100">
                            <template x-for="permission in (permissions || [])" :key="permission?.id || Math.random()">
                                <tr class="hover:bg-slate-50 transition-colors hover-lift">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div>
                                                <div class="text-sm font-semibold text-slate-900" x-text="permission?.name || permission?.username || ''"></div>
                                                <div class="text-sm text-slate-500 flex items-center gap-2">
                                                    <span x-text="permission?.username || ''"></span>
                                                    <span class="px-2 py-1 text-xs rounded-full font-medium"
                                                          :class="getRoleBadgeClass(permission?.role)"
                                                          x-text="getRoleText(permission?.role)"></span>
                                                </div>
                                                <div class="text-xs text-slate-400 mt-1" x-show="permission?.org_name" x-text="permission?.org_name || ''"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-slate-900" x-text="getPermissionLabel(permission?.permission_name)"></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full"
                                              :class="permission?.permission_value === 'allow' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                                            <i :class="permission?.permission_value === 'allow' ? 'fas fa-check' : 'fas fa-times'" class="mr-1"></i>
                                            <span x-text="permission?.permission_value === 'allow' ? '允许' : '禁止'"></span>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <label class="toggle-switch">
                                            <input type="checkbox" :checked="permission?.is_enabled"
                                                   @change="togglePermissionStatus(permission)">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500" x-text="permission?.created_at || ''"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center gap-2">
                                            <button @click="editPermission(permission)"
                                                    class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                                                <i class="fas fa-edit mr-1"></i>
                                                编辑
                                            </button>
                                            <button @click="deletePermission(permission?.id)"
                                                    class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-600 bg-red-50 rounded-lg hover:bg-red-100 transition-colors">
                                                <i class="fas fa-trash mr-1"></i>
                                                删除
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                    
                    <!-- 空状态 -->
                    <div x-show="permissions.length === 0" class="p-12 text-center">
                        <div class="w-16 h-16 mx-auto mb-4 bg-slate-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-shield-alt text-2xl text-slate-400"></i>
                        </div>
                        <h3 class="text-lg font-medium text-slate-900 mb-2">暂无权限记录</h3>
                        <p class="text-slate-500 mb-6">还没有设置任何用户权限</p>
                        <button @click="openAddModal()"
                                class="btn-primary h-10 px-4 text-white rounded-lg flex items-center gap-2 mx-auto">
                            <i class="fas fa-plus"></i>
                            <span>添加权限</span>
                        </button>
                    </div>
                </div>

                <!-- 分页 -->
                <div x-show="totalPages > 1" class="px-6 py-4 border-t border-slate-200 bg-slate-50/50">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-slate-600">
                            显示第 <span class="font-medium text-slate-900" x-text="(currentPage - 1) * perPage + 1"></span> 到
                            <span class="font-medium text-slate-900" x-text="Math.min(currentPage * perPage, total)"></span> 条，
                            共 <span class="font-medium text-slate-900" x-text="total"></span> 条记录
                        </div>
                        <div class="flex items-center gap-2">
                            <button @click="changePage(currentPage - 1)"
                                    :disabled="currentPage <= 1"
                                    :class="currentPage <= 1 ? 'bg-slate-100 text-slate-400 cursor-not-allowed' : 'bg-white text-slate-700 hover:bg-slate-50'"
                                    class="px-4 py-2 border border-slate-300 rounded-xl text-sm transition-colors">
                                <i class="fas fa-chevron-left mr-1"></i>
                                上一页
                            </button>
                            <template x-for="page in (getPageNumbers() || [])" :key="page || Math.random()">
                                <button @click="changePage(page)"
                                        :class="page === currentPage ? 'btn-primary text-white' : 'bg-white text-slate-700 hover:bg-slate-50'"
                                        class="px-4 py-2 border border-slate-300 rounded-xl text-sm transition-colors"
                                        x-text="page || ''"></button>
                            </template>
                            <button @click="changePage(currentPage + 1)"
                                    :disabled="currentPage >= totalPages"
                                    :class="currentPage >= totalPages ? 'bg-slate-100 text-slate-400 cursor-not-allowed' : 'bg-white text-slate-700 hover:bg-slate-50'"
                                    class="px-4 py-2 border border-slate-300 rounded-xl text-sm transition-colors">
                                下一页
                                <i class="fas fa-chevron-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户选择模态框 -->
        <div x-show="showUserSelectModal" x-cloak class="fixed inset-0 z-[60] overflow-y-auto">
            <div class="modal-overlay flex items-center justify-center p-4">
                <div class="bg-white rounded-2xl shadow-2xl w-full max-w-5xl overflow-hidden flex flex-col" style="height: 600px;">
                    <!-- 模态框头部 -->
                    <div class="flex items-center justify-between px-6 py-4 border-b border-slate-200 bg-gradient-to-r from-blue-50 to-indigo-50 flex-shrink-0" style="height: 80px;">
                        <div class="flex items-center space-x-3">
                            <button @click="closeUserSelectModal()"
                                    class="w-8 h-8 bg-white hover:bg-slate-50 text-slate-600 rounded-lg flex items-center justify-center transition-colors shadow-sm">
                                <i class="fas fa-arrow-left text-sm"></i>
                            </button>
                            <div>
                                <h3 class="text-lg font-bold text-slate-800">选择用户</h3>
                                <p class="text-xs text-slate-500">选择要设置权限的用户</p>
                            </div>
                        </div>
                        <button @click="closeUserSelectModal()"
                                class="w-8 h-8 bg-white hover:bg-slate-50 text-slate-600 rounded-lg flex items-center justify-center transition-colors shadow-sm">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>

                    <!-- 模态框内容 -->
                    <div class="flex-1 overflow-hidden" style="height: 520px;">
                        <div class="flex h-full">
                            <!-- 左侧：组织列表 -->
                            <div class="w-80 flex-shrink-0 bg-gradient-to-b from-slate-50 to-slate-100 border-r border-slate-200" style="height: 520px;">
                                <div class="flex flex-col h-full">
                                    <div class="p-4 border-b border-slate-200">
                                        <h4 class="font-medium text-slate-900 mb-3">选择组织</h4>

                                        <!-- 组织类型筛选 -->
                                        <div class="mb-3">
                                            <div class="custom-select" :class="{ 'active': orgTypeDropdownOpen }">
                                                <div class="custom-select-trigger" @click="orgTypeDropdownOpen = !orgTypeDropdownOpen">
                                                    <span class="custom-select-text" x-text="getOrgTypeText()">全部类型</span>
                                                    <i class="fas fa-chevron-down custom-select-arrow" :class="{ 'rotate-180': orgTypeDropdownOpen }"></i>
                                                </div>
                                                <div class="custom-select-dropdown" :class="{ 'active': orgTypeDropdownOpen }">
                                                    <div class="custom-select-options">
                                                        <div class="custom-select-option" @click="selectOrgType(''); orgTypeDropdownOpen = false"
                                                             :class="{ 'selected': orgFilter.type === '' }">全部类型</div>
                                                        <div class="custom-select-option" @click="selectOrgType('school'); orgTypeDropdownOpen = false"
                                                             :class="{ 'selected': orgFilter.type === 'school' }">学校</div>
                                                        <div class="custom-select-option" @click="selectOrgType('publisher'); orgTypeDropdownOpen = false"
                                                             :class="{ 'selected': orgFilter.type === 'publisher' }">出版社</div>
                                                        <div class="custom-select-option" @click="selectOrgType('dealer'); orgTypeDropdownOpen = false"
                                                             :class="{ 'selected': orgFilter.type === 'dealer' }">经销商</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 组织搜索 -->
                                        <input type="text" x-model="orgFilter.search" @input="debounceOrgSearch()"
                                               placeholder="搜索组织名称"
                                               class="w-full px-3 py-2 border border-slate-300 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <!-- 组织列表 -->
                                    <div class="flex-1 overflow-y-auto custom-scrollbar">
                                        <div x-show="orgLoading" class="p-4 text-center text-slate-500">
                                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                                        </div>

                                        <!-- 分页信息 -->
                                        <div class="p-2 bg-blue-50 text-xs border-b" x-show="!orgLoading && allOrganizations.length > 0">
                                            <div class="flex justify-between items-center">
                                                <div>
                                                    <span>总计: <span x-text="allOrganizations.length"></span> 个组织</span>
                                                    <span class="ml-2">第 <span x-text="orgPage"></span>/<span x-text="orgTotalPages"></span> 页</span>
                                                </div>
                                                <div class="flex gap-1">
                                                    <button @click="changeOrgPage(orgPage - 1)"
                                                            :disabled="orgPage <= 1"
                                                            :class="orgPage <= 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-200'"
                                                            class="px-2 py-1 bg-blue-100 rounded text-xs">
                                                        上一页
                                                    </button>
                                                    <button @click="changeOrgPage(orgPage + 1)"
                                                            :disabled="orgPage >= orgTotalPages"
                                                            :class="orgPage >= orgTotalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-200'"
                                                            class="px-2 py-1 bg-blue-100 rounded text-xs">
                                                        下一页
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <template x-for="org in (displayedOrganizations || [])" :key="org?.id || Math.random()">
                                            <div @click="selectOrganization(org)"
                                                 :class="selectedOrg?.id === org?.id ? 'bg-blue-100 border-blue-200' : 'hover:bg-slate-100'"
                                                 class="p-3 border-b border-slate-100 cursor-pointer transition-colors">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex-1">
                                                        <div class="font-medium text-slate-900" x-text="org?.name || '未知组织'"></div>
                                                        <div class="text-xs text-slate-500" x-text="getOrgTypeLabel(org?.type) || '未知类型'"></div>
                                                    </div>
                                                    <div class="flex items-center gap-1 px-2 py-1 bg-slate-100 rounded-full">
                                                        <i class="fas fa-users text-xs text-slate-500"></i>
                                                        <span class="text-xs font-medium text-slate-600" x-text="org?.user_count || 0"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                        <div x-show="!orgLoading && (displayedOrganizations || []).length === 0" class="p-4 text-center text-slate-500">
                                            暂无组织
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 右侧：用户列表 -->
                            <div class="flex-1 flex flex-col">
                                <div class="p-4 border-b border-slate-200 bg-white">
                                    <h4 class="font-medium text-slate-900 mb-3">选择用户</h4>

                                    <!-- 用户搜索 -->
                                    <input type="text" x-model="userFilter.search" @input="debounceUserSearch()"
                                           placeholder="搜索用户名或姓名"
                                           class="w-full px-3 py-2 border border-slate-300 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>

                                <!-- 用户列表 -->
                                <div class="flex-1 overflow-y-auto custom-scrollbar bg-white">
                                    <div x-show="userLoading" class="p-4 text-center text-slate-500">
                                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                                    </div>
                                    <template x-for="user in (users || [])" :key="user?.user_id || Math.random()">
                                        <div @click="toggleUserSelection(user)"
                                             :class="isUserSelected(user) ? 'bg-blue-50 border-blue-200' : 'hover:bg-slate-50'"
                                             class="p-3 border-b border-slate-100 cursor-pointer flex items-center transition-colors">
                                            <input type="checkbox" :checked="isUserSelected(user)"
                                                   class="mr-3 text-blue-600 rounded">
                                            <div class="flex-1">
                                                <div class="font-medium text-slate-900" x-text="user?.name || user?.username || ''"></div>
                                                <div class="text-xs text-slate-500 flex items-center gap-2">
                                                    <span x-text="user?.username || ''"></span>
                                                    <span class="px-2 py-1 text-xs rounded-full font-medium"
                                                          :class="getRoleBadgeClass(user?.role)"
                                                          x-text="getRoleText(user?.role)"></span>
                                                </div>
                                                <div class="text-xs text-slate-400 mt-1" x-show="user?.org_name" x-text="user?.org_name || ''"></div>
                                            </div>
                                        </div>
                                    </template>
                                    <div x-show="!userLoading && users.length === 0" class="p-4 text-center text-slate-500">
                                        <span x-show="!selectedOrg">请先选择组织</span>
                                        <span x-show="selectedOrg">该组织暂无用户</span>
                                    </div>
                                </div>

                                <!-- 已选用户显示 -->
                                <div x-show="selectedUsers.length > 0" class="p-4 border-t border-slate-200 bg-slate-50">
                                    <label class="block text-sm font-medium text-slate-700 mb-2">已选用户 (<span x-text="selectedUsers.length"></span>)</label>
                                    <div class="max-h-20 overflow-y-auto custom-scrollbar">
                                        <template x-for="user in (selectedUsers || [])" :key="user?.user_id || Math.random()">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2 mb-1">
                                                <span x-text="user?.name || user?.username || ''"></span>
                                                <button type="button" @click="removeUserSelection(user)" class="ml-1 text-blue-600 hover:text-blue-800">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </span>
                                        </template>
                                    </div>

                                    <!-- 确认按钮 -->
                                    <div class="mt-4 flex justify-end">
                                        <button @click="confirmUserSelection()"
                                                class="btn-primary h-10 px-6 text-white rounded-xl flex items-center gap-2">
                                            <i class="fas fa-check"></i>
                                            <span>确认选择</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 权限设置模态框 -->
        <div x-show="showPermissionModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto">
            <div class="modal-overlay flex items-center justify-center p-4">
                <div class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
                    <div class="px-6 py-4 border-b border-slate-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                        <h3 class="text-lg font-semibold text-slate-800" x-text="editingPermission ? '编辑权限' : '设置权限'"></h3>
                    </div>
                    <form @submit.prevent="savePermission()" class="flex flex-col max-h-[calc(90vh-80px)]">
                        <!-- 已选用户显示 -->
                        <div class="p-6 border-b border-slate-200" x-show="!editingPermission">
                            <label class="block text-sm font-medium text-slate-700 mb-3">已选用户 (<span x-text="selectedUsers.length"></span>)</label>
                            <div class="max-h-32 overflow-y-auto custom-scrollbar border border-slate-200 rounded-xl p-3 bg-slate-50">
                                <template x-for="user in (selectedUsers || [])" :key="user?.user_id || Math.random()">
                                    <div class="flex items-center justify-between p-2 bg-white rounded-lg mb-2 last:mb-0">
                                        <div class="flex items-center gap-3">
                                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-user text-blue-600 text-sm"></i>
                                            </div>
                                            <div>
                                                <div class="font-medium text-slate-900" x-text="user?.name || user?.username || ''"></div>
                                                <div class="text-xs text-slate-500 flex items-center gap-2">
                                                    <span x-text="user?.username || ''"></span>
                                                    <span class="px-2 py-1 text-xs rounded-full font-medium"
                                                          :class="getRoleBadgeClass(user?.role)"
                                                          x-text="getRoleText(user?.role)"></span>
                                                </div>
                                                <div class="text-xs text-slate-400" x-show="user?.org_name" x-text="user?.org_name || ''"></div>
                                            </div>
                                        </div>
                                        <button type="button" @click="removeUserSelection(user)"
                                                class="w-6 h-6 text-slate-400 hover:text-red-500 transition-colors">
                                            <i class="fas fa-times text-sm"></i>
                                        </button>
                                    </div>
                                </template>
                            </div>
                            <button type="button" @click="addMoreUsers()"
                                    class="mt-3 w-full h-10 border-2 border-dashed border-slate-300 rounded-xl text-slate-500 hover:border-blue-400 hover:text-blue-600 transition-colors flex items-center justify-center gap-2">
                                <i class="fas fa-plus"></i>
                                <span>添加更多用户</span>
                            </button>
                        </div>

                        <!-- 编辑模式：显示当前用户 -->
                        <div class="p-6 border-b border-slate-200" x-show="editingPermission">
                            <label class="block text-sm font-medium text-slate-700 mb-3">当前用户</label>
                            <div class="flex items-center gap-3 p-3 bg-slate-50 rounded-xl">
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-blue-600"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-slate-900" x-text="editingPermission?.name || editingPermission?.username"></div>
                                    <div class="text-sm text-slate-500 flex items-center gap-2">
                                        <span x-text="editingPermission?.username"></span>
                                        <span class="px-2 py-1 text-xs rounded-full font-medium"
                                              :class="getRoleBadgeClass(editingPermission?.role)"
                                              x-text="getRoleText(editingPermission?.role)"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 权限设置区域 -->
                        <div class="flex-1 p-6 space-y-6">
                            <div class="grid grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">权限名称 <span class="text-red-500">*</span></label>
                                    <div class="custom-select" :class="{ 'active': modalPermissionDropdownOpen }">
                                        <div class="custom-select-trigger" @click="modalPermissionDropdownOpen = !modalPermissionDropdownOpen">
                                            <span class="custom-select-text" x-text="getModalPermissionText()">请选择权限</span>
                                            <i class="fas fa-chevron-down custom-select-arrow" :class="{ 'rotate-180': modalPermissionDropdownOpen }"></i>
                                        </div>
                                        <div class="custom-select-dropdown" :class="{ 'active': modalPermissionDropdownOpen }">
                                            <div class="custom-select-options">
                                                <template x-for="option in (permissionOptions || [])" :key="option?.value || Math.random()">
                                                    <div class="custom-select-option" @click="selectModalPermission(option?.value || ''); modalPermissionDropdownOpen = false"
                                                         :class="{ 'selected': form.permission_name === (option?.value || '') }" x-text="option?.label || ''"></div>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">权限值 <span class="text-red-500">*</span></label>
                                    <div class="grid grid-cols-2 gap-2">
                                        <label class="flex items-center p-3 border border-slate-300 rounded-xl cursor-pointer hover:bg-slate-50 transition-colors"
                                               :class="form.permission_value === 'allow' ? 'border-green-500 bg-green-50' : ''">
                                            <input type="radio" x-model="form.permission_value" value="allow" class="sr-only">
                                            <div class="flex items-center gap-2">
                                                <i class="fas fa-check text-green-600"></i>
                                                <span class="text-sm font-medium text-slate-700">允许</span>
                                            </div>
                                        </label>
                                        <label class="flex items-center p-3 border border-slate-300 rounded-xl cursor-pointer hover:bg-slate-50 transition-colors"
                                               :class="form.permission_value === 'deny' ? 'border-red-500 bg-red-50' : ''">
                                            <input type="radio" x-model="form.permission_value" value="deny" class="sr-only">
                                            <div class="flex items-center gap-2">
                                                <i class="fas fa-times text-red-600"></i>
                                                <span class="text-sm font-medium text-slate-700">禁止</span>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">状态</label>
                                <label class="toggle-switch">
                                    <input type="checkbox" :checked="form.is_enabled" @change="form.is_enabled = $event.target.checked">
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="ml-3 text-sm text-slate-600" x-text="form.is_enabled ? '启用' : '禁用'"></span>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">备注</label>
                                <textarea x-model="form.remark" rows="3"
                                          class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                                          placeholder="权限设置的备注说明..."></textarea>
                            </div>
                        </div>

                        <!-- 表单底部按钮 -->
                        <div class="p-6 border-t border-slate-200 bg-slate-50/50">
                            <div class="flex justify-end gap-3">
                                <button type="button" @click="closePermissionModal()"
                                        class="h-12 px-6 text-slate-700 bg-white border border-slate-300 hover:bg-slate-50 rounded-xl transition-colors">
                                    取消
                                </button>
                                <button type="submit" :disabled="saving || (!editingPermission && selectedUsers.length === 0)"
                                        class="btn-primary h-12 px-6 text-white rounded-xl transition-colors disabled:opacity-50 flex items-center gap-2">
                                    <i class="fas fa-save"></i>
                                    <span x-show="!saving" x-text="editingPermission ? '更新权限' : '批量设置'"></span>
                                    <span x-show="saving">保存中...</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 消息提示 -->
        <div x-show="message.show" x-cloak 
             class="fixed top-4 right-4 z-50 max-w-sm w-full">
            <div class="rounded-lg shadow-lg p-4"
                 :class="message.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'">
                <div class="flex items-center">
                    <i :class="message.type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'" class="mr-2"></i>
                    <span x-text="message.text"></span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('全局错误:', e.error);
            console.error('错误堆栈:', e.error?.stack);
            console.error('错误位置:', e.filename, e.lineno, e.colno);
        });

        // Alpine.js错误处理
        document.addEventListener('alpine:init', () => {
            console.log('Alpine.js 开始初始化...');
        });

        // 监听Alpine.js的错误
        document.addEventListener('alpine:initialized', () => {
            console.log('Alpine.js 初始化完成');
        });

        // 捕获模板渲染错误
        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise拒绝:', event.reason);
            console.error('Promise拒绝堆栈:', event.reason?.stack);
        });

        function userPermissionManager() {
            return {
                // 数据状态
                permissions: [],
                originalPermissions: [], // 存储原始数据用于排序
                permissionOptions: [],
                loading: false,
                saving: false,
                
                // 分页
                currentPage: 1,
                perPage: 20,
                total: 0,
                totalPages: 0,
                
                // 筛选
                filters: {
                    search: '',
                    permission_name: '',
                    permission_value: ''
                },
                
                // 模态框
                showUserSelectModal: false,
                showPermissionModal: false,
                editingPermission: null,
                saving: false,

                // 下拉框状态
                permissionFilterDropdownOpen: false,
                permissionValueDropdownOpen: false,
                modalPermissionDropdownOpen: false,
                
                // 表单
                form: {
                    permission_name: '',
                    permission_value: 'allow',
                    is_enabled: true,
                    remark: ''
                },

                // 组织和用户选择
                organizations: [],
                allOrganizations: [], // 存储所有组织数据
                displayedOrganizations: [], // 当前显示的组织数据
                orgPage: 1,
                orgPageSize: 50, // 每页显示50个组织
                orgTotalPages: 0,
                users: [],
                selectedOrg: null,
                selectedUsers: [],
                orgLoading: false,
                userLoading: false,
                orgTypeDropdownOpen: false,

                // 筛选条件
                orgFilter: {
                    type: '',
                    search: ''
                },
                userFilter: {
                    search: ''
                },

                // 搜索防抖
                orgSearchTimeout: null,
                userSearchTimeout: null,

                // 排序状态
                sortField: '',
                sortOrder: '',
                
                // 消息提示
                message: {
                    show: false,
                    type: 'success',
                    text: ''
                },
                
                // 安全数组检查方法
                ensureArray(arr, name) {
                    if (!Array.isArray(arr)) {
                        console.warn(`${name} 不是数组，重置为空数组:`, arr);
                        return [];
                    }
                    return arr;
                },

                // 初始化
                async init() {
                    console.log('Alpine.js 初始化开始...');

                    // 确保所有数组都被正确初始化
                    this.permissions = this.ensureArray(this.permissions, 'permissions');
                    this.originalPermissions = this.ensureArray(this.originalPermissions, 'originalPermissions');
                    this.permissionOptions = this.ensureArray(this.permissionOptions, 'permissionOptions');
                    this.allOrganizations = this.ensureArray(this.allOrganizations, 'allOrganizations');
                    this.displayedOrganizations = this.ensureArray(this.displayedOrganizations, 'displayedOrganizations');
                    this.users = this.ensureArray(this.users, 'users');
                    this.selectedUsers = this.ensureArray(this.selectedUsers, 'selectedUsers');

                    console.log('初始数据状态:', {
                        permissions: this.permissions,
                        organizations: this.organizations,
                        users: this.users,
                        selectedUsers: this.selectedUsers,
                        permissionOptions: this.permissionOptions
                    });

                    await this.loadPermissionOptions();
                    await this.loadPermissions();
                    this.setupGlobalClickHandler();

                    console.log('Alpine.js 初始化完成');
                },

                // 设置全局点击事件处理器
                setupGlobalClickHandler() {
                    document.addEventListener('click', (event) => {
                        // 检查点击是否在下拉框内
                        const isInsideDropdown = event.target.closest('.custom-select');

                        if (!isInsideDropdown) {
                            // 点击在下拉框外部，关闭所有下拉框
                            this.permissionFilterDropdownOpen = false;
                            this.permissionValueDropdownOpen = false;
                            this.orgTypeDropdownOpen = false;
                            this.modalPermissionDropdownOpen = false;
                        }
                    });
                },

                // 打开添加模态框时初始化
                async openAddModal() {
                    this.selectedUsers = [];
                    // 重置组织筛选状态
                    this.allOrganizations = [];
                    this.displayedOrganizations = [];
                    this.users = [];
                    this.selectedOrg = null;
                    this.orgFilter = { type: '', search: '' };
                    this.userFilter = { search: '' };
                    this.orgTypeDropdownOpen = false;
                    this.orgPage = 1;

                    this.showUserSelectModal = true;
                    // 使用setTimeout确保在模态框显示后再加载数据
                    setTimeout(async () => {
                        await this.loadOrganizations();
                    }, 100);
                },

                // 打开用户选择模态框
                async openUserSelectModal() {
                    // 重置状态
                    this.allOrganizations = [];
                    this.displayedOrganizations = [];
                    this.users = [];
                    this.selectedOrg = null;
                    this.orgFilter = { type: '', search: '' };
                    this.userFilter = { search: '' };
                    this.orgTypeDropdownOpen = false;
                    this.orgPage = 1;

                    this.showUserSelectModal = true;
                    // 使用setTimeout确保在模态框显示后再加载数据
                    setTimeout(async () => {
                        await this.loadOrganizations();
                    }, 100);
                },

                // 关闭用户选择模态框
                closeUserSelectModal() {
                    this.showUserSelectModal = false;
                    this.allOrganizations = [];
                    this.displayedOrganizations = [];
                    this.users = [];
                    this.selectedOrg = null;
                    this.orgFilter = { type: '', search: '' };
                    this.userFilter = { search: '' };
                    this.orgTypeDropdownOpen = false;
                    this.orgPage = 1;
                },

                // 添加更多用户
                async addMoreUsers() {
                    await this.openUserSelectModal();
                },

                // 确认用户选择
                confirmUserSelection() {
                    if (this.selectedUsers.length === 0) {
                        this.showMessage('请至少选择一个用户', 'error');
                        return;
                    }
                    this.closeUserSelectModal();
                    this.showPermissionModal = true;
                },

                // 关闭权限设置模态框
                closePermissionModal() {
                    this.showPermissionModal = false;
                    this.editingPermission = null;
                    this.modalPermissionDropdownOpen = false;
                    this.form = {
                        permission_name: '',
                        permission_value: 'allow',
                        is_enabled: true,
                        remark: ''
                    };
                },
                
                // 加载权限选项
                async loadPermissionOptions() {
                    console.log('开始加载权限选项...');
                    try {
                        const response = await fetch('/api/admin/get_permission_options');
                        const result = await response.json();
                        console.log('权限选项API响应:', result);

                        if (result.code === 0) {
                            this.permissionOptions = this.ensureArray(result.data, 'permissionOptions API结果');
                            console.log('设置权限选项:', this.permissionOptions);
                            console.log('权限选项数组长度:', this.permissionOptions.length);

                            // 检查每个权限选项的结构
                            this.permissionOptions.forEach((option, index) => {
                                console.log(`权限选项${index}:`, option);
                                if (!option || typeof option !== 'object') {
                                    console.error(`权限选项${index}不是有效对象:`, option);
                                }
                            });
                        } else {
                            console.error('权限选项API返回错误:', result.message);
                            this.permissionOptions = [];
                        }
                    } catch (error) {
                        console.error('加载权限选项失败:', error);
                        console.error('错误堆栈:', error.stack);
                        this.permissionOptions = [];
                    }
                },
                
                // 加载权限列表
                async loadPermissions() {
                    this.loading = true;
                    try {
                        const params = new URLSearchParams({
                            page: this.currentPage,
                            per_page: this.perPage,
                            ...this.filters
                        });
                        
                        const response = await fetch(`/api/admin/get_user_permissions?${params}`);
                        const result = await response.json();
                        
                        if (result.code === 0) {
                            this.originalPermissions = result.data.permissions;
                            this.total = result.data.total;
                            this.totalPages = result.data.total_pages;

                            // 应用当前排序
                            this.applySorting();
                        } else {
                            this.showMessage(result.message, 'error');
                        }
                    } catch (error) {
                        this.showMessage('加载权限列表失败', 'error');
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 加载组织列表
                async loadOrganizations() {
                    this.orgLoading = true;
                    try {
                        const params = new URLSearchParams();

                        // 只有当type不为空时才添加type参数
                        if (this.orgFilter.type) {
                            params.append('type', this.orgFilter.type);
                        }

                        // 只有当search不为空时才添加search参数
                        if (this.orgFilter.search) {
                            params.append('search', this.orgFilter.search);
                        }

                        const response = await fetch(`/api/admin/get_organizations?${params}`);
                        const result = await response.json();

                        if (result.code === 0) {
                            this.allOrganizations = this.ensureArray(result.data, 'organizations API结果');

                            // 重置分页
                            this.orgPage = 1;
                            this.orgTotalPages = Math.ceil(this.allOrganizations.length / this.orgPageSize);

                            // 更新显示的组织
                            this.updateDisplayedOrganizations();
                        } else {
                            this.showMessage(result.message, 'error');
                            this.allOrganizations = [];
                            this.displayedOrganizations = [];
                        }
                    } catch (error) {
                        this.showMessage('加载组织列表失败', 'error');
                        this.allOrganizations = [];
                        this.displayedOrganizations = [];
                    } finally {
                        this.orgLoading = false;
                    }
                },

                // 更新显示的组织（分页）
                updateDisplayedOrganizations() {
                    const startIndex = (this.orgPage - 1) * this.orgPageSize;
                    const endIndex = startIndex + this.orgPageSize;
                    this.displayedOrganizations = this.allOrganizations.slice(startIndex, endIndex);
                    console.log(`显示组织: ${startIndex}-${endIndex}, 实际数量: ${this.displayedOrganizations.length}`);
                },

                // 组织分页
                changeOrgPage(page) {
                    if (page >= 1 && page <= this.orgTotalPages) {
                        this.orgPage = page;
                        this.updateDisplayedOrganizations();
                    }
                },

                // 获取组织分页页码
                getOrgPageNumbers() {
                    const pages = [];
                    const start = Math.max(1, this.orgPage - 2);
                    const end = Math.min(this.orgTotalPages, this.orgPage + 2);

                    for (let i = start; i <= end; i++) {
                        pages.push(i);
                    }
                    return pages;
                },

                // 选择组织
                async selectOrganization(org) {
                    console.log('选择组织:', org);
                    console.log('组织对象类型:', typeof org);
                    console.log('组织对象属性:', Object.keys(org || {}));

                    this.selectedOrg = org;
                    this.users = [];
                    this.userFilter.search = '';
                    await this.loadUsers();
                },

                // 加载用户列表
                async loadUsers() {
                    if (!this.selectedOrg) {
                        this.users = [];
                        return;
                    }

                    this.userLoading = true;
                    try {
                        const params = new URLSearchParams({
                            org_type: this.selectedOrg.type,
                            org_id: this.selectedOrg.id,
                            search: this.userFilter.search
                        });

                        const response = await fetch(`/api/admin/get_users_by_org?${params}`);
                        const result = await response.json();
                        if (result.code === 0) {
                            this.users = result.data || [];
                        } else {
                            this.showMessage(result.message, 'error');
                        }
                    } catch (error) {
                        this.showMessage('加载用户列表失败', 'error');
                    } finally {
                        this.userLoading = false;
                    }
                },

                // 切换用户选择状态
                toggleUserSelection(user) {
                    const index = this.selectedUsers.findIndex(u => u.user_id === user.user_id);
                    if (index > -1) {
                        this.selectedUsers.splice(index, 1);
                    } else {
                        this.selectedUsers.push(user);
                    }
                },

                // 检查用户是否已选择
                isUserSelected(user) {
                    return this.selectedUsers.some(u => u.user_id === user.user_id);
                },

                // 移除用户选择
                removeUserSelection(user) {
                    const index = this.selectedUsers.findIndex(u => u.user_id === user.user_id);
                    if (index > -1) {
                        this.selectedUsers.splice(index, 1);
                    }
                },

                // 组织搜索防抖
                debounceOrgSearch() {
                    if (this.orgSearchTimeout) {
                        clearTimeout(this.orgSearchTimeout);
                    }
                    this.orgSearchTimeout = setTimeout(() => {
                        this.loadOrganizations();
                    }, 500);
                },

                // 用户搜索防抖
                debounceUserSearch() {
                    if (this.userSearchTimeout) {
                        clearTimeout(this.userSearchTimeout);
                    }
                    this.userSearchTimeout = setTimeout(() => {
                        this.loadUsers();
                    }, 500);
                },
                
                // 编辑权限
                editPermission(permission) {
                    this.editingPermission = permission;
                    this.form = {
                        permission_name: permission.permission_name,
                        permission_value: permission.permission_value,
                        is_enabled: Boolean(permission.is_enabled),
                        remark: permission.remark || ''
                    };
                    this.showPermissionModal = true;
                },
                
                // 保存权限
                async savePermission() {
                    if (this.editingPermission) {
                        // 编辑模式：验证权限信息
                        if (!this.form.permission_name) {
                            this.showMessage('请填写权限信息', 'error');
                            return;
                        }
                    } else {
                        // 添加模式：验证用户和权限信息
                        if (this.selectedUsers.length === 0 || !this.form.permission_name) {
                            this.showMessage('请选择用户并填写权限信息', 'error');
                            return;
                        }
                    }

                    this.saving = true;
                    try {
                        if (this.editingPermission) {
                            // 编辑模式：只更新单个权限
                            const response = await fetch('/api/admin/add_user_permission', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    user_id: this.editingPermission.user_id,
                                    permission_name: this.form.permission_name,
                                    permission_value: this.form.permission_value,
                                    is_enabled: this.form.is_enabled ? 1 : 0,
                                    remark: this.form.remark
                                })
                            });

                            const result = await response.json();
                            if (result.code === 0) {
                                this.showMessage(result.message, 'success');
                                this.closePermissionModal();
                                await this.loadPermissions();
                            } else {
                                this.showMessage(result.message, 'error');
                            }
                        } else {
                            // 添加模式：批量添加权限
                            let successCount = 0;
                            let errorCount = 0;

                            for (const user of this.selectedUsers) {
                                try {
                                    const response = await fetch('/api/admin/add_user_permission', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        },
                                        body: JSON.stringify({
                                            user_id: user.user_id,
                                            permission_name: this.form.permission_name,
                                            permission_value: this.form.permission_value,
                                            is_enabled: this.form.is_enabled ? 1 : 0,
                                            remark: this.form.remark
                                        })
                                    });

                                    const result = await response.json();
                                    if (result.code === 0) {
                                        successCount++;
                                    } else {
                                        errorCount++;
                                    }
                                } catch (error) {
                                    errorCount++;
                                }
                            }

                            if (successCount > 0) {
                                this.showMessage(`成功添加 ${successCount} 个用户权限${errorCount > 0 ? `，失败 ${errorCount} 个` : ''}`, 'success');
                                this.closePermissionModal();
                                this.selectedUsers = [];
                                await this.loadPermissions();
                            } else {
                                this.showMessage('批量添加失败', 'error');
                            }
                        }
                    } catch (error) {
                        this.showMessage('保存失败', 'error');
                    } finally {
                        this.saving = false;
                    }
                },
                
                // 删除权限
                async deletePermission(permissionId) {
                    if (!confirm('确定要删除这个权限设置吗？')) {
                        return;
                    }
                    
                    try {
                        const response = await fetch('/api/admin/delete_user_permission', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                permission_id: permissionId
                            })
                        });
                        
                        const result = await response.json();
                        if (result.code === 0) {
                            this.showMessage(result.message, 'success');
                            await this.loadPermissions();
                        } else {
                            this.showMessage(result.message, 'error');
                        }
                    } catch (error) {
                        this.showMessage('删除失败', 'error');
                    }
                },
                
                // 切换权限状态
                async togglePermissionStatus(permission) {
                    try {
                        const response = await fetch('/api/admin/add_user_permission', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                user_id: permission.user_id,
                                permission_name: permission.permission_name,
                                permission_value: permission.permission_value,
                                is_enabled: permission.is_enabled ? 0 : 1,
                                remark: permission.remark
                            })
                        });

                        const result = await response.json();
                        if (result.code === 0) {
                            permission.is_enabled = !permission.is_enabled;
                            this.showMessage('状态更新成功', 'success');
                        } else {
                            this.showMessage(result.message, 'error');
                        }
                    } catch (error) {
                        this.showMessage('状态更新失败', 'error');
                    }
                },
                
                // 重置筛选
                resetFilters() {
                    this.filters = {
                        search: '',
                        permission_name: '',
                        permission_value: ''
                    };
                    this.permissionFilterDropdownOpen = false;
                    this.permissionValueDropdownOpen = false;
                    this.currentPage = 1;
                    this.loadPermissions();
                },
                
                // 防抖搜索
                debounceSearch() {
                    if (this.searchTimeout) {
                        clearTimeout(this.searchTimeout);
                    }
                    this.searchTimeout = setTimeout(() => {
                        this.currentPage = 1;
                        this.loadPermissions();
                    }, 500);
                },
                
                // 分页
                changePage(page) {
                    if (page >= 1 && page <= this.totalPages) {
                        this.currentPage = page;
                        this.loadPermissions();
                    }
                },
                
                getPageNumbers() {
                    const pages = [];
                    const start = Math.max(1, this.currentPage - 2);
                    const end = Math.min(this.totalPages, this.currentPage + 2);
                    
                    for (let i = start; i <= end; i++) {
                        pages.push(i);
                    }
                    return pages;
                },
                
                // 辅助方法
                getPermissionLabel(permissionName) {
                    if (!permissionName || !this.permissionOptions || this.permissionOptions.length === 0) {
                        return permissionName || '';
                    }
                    const option = this.permissionOptions.find(opt => opt && opt.value === permissionName);
                    return option ? option.label : permissionName;
                },

                // 获取权限筛选显示文本
                getPermissionFilterText() {
                    if (!this.filters || !this.filters.permission_name) return '全部权限';
                    if (!this.permissionOptions || this.permissionOptions.length === 0) return '全部权限';
                    const option = this.permissionOptions.find(opt => opt && opt.value === this.filters.permission_name);
                    return option ? option.label : '全部权限';
                },

                // 获取权限值显示文本
                getPermissionValueText() {
                    if (!this.filters) return '全部';
                    const valueMap = {
                        '': '全部',
                        'allow': '允许',
                        'deny': '禁止'
                    };
                    return valueMap[this.filters.permission_value] || '全部';
                },

                // 获取模态框权限显示文本
                getModalPermissionText() {
                    if (!this.form || !this.form.permission_name) return '请选择权限';
                    if (!this.permissionOptions || this.permissionOptions.length === 0) return '请选择权限';
                    const option = this.permissionOptions.find(opt => opt && opt.value === this.form.permission_name);
                    return option ? option.label : '请选择权限';
                },

                // 选择权限筛选
                selectPermissionFilter(value) {
                    this.filters.permission_name = value;
                    this.loadPermissions();
                },

                // 选择权限值筛选
                selectPermissionValue(value) {
                    this.filters.permission_value = value;
                    this.loadPermissions();
                },

                // 选择模态框权限
                selectModalPermission(value) {
                    this.form.permission_name = value;
                },

                // 获取组织类型显示文本
                getOrgTypeText() {
                    if (!this.orgFilter) return '全部类型';
                    const typeMap = {
                        '': '全部类型',
                        'school': '学校',
                        'publisher': '出版社',
                        'dealer': '经销商'
                    };
                    return typeMap[this.orgFilter.type] || '全部类型';
                },

                // 选择组织类型
                selectOrgType(value) {
                    this.orgFilter.type = value;
                    // 重置选中的组织和用户
                    this.selectedOrg = null;
                    this.users = [];
                    this.orgPage = 1; // 重置分页
                    this.loadOrganizations();
                },
                
                getRoleText(role) {
                    if (!role) return '';
                    const roleMap = {
                        'admin': '管理员',
                        'teacher': '教师',
                        'publisher': '出版社',
                        'dealer': '经销商'
                    };
                    return roleMap[role] || role;
                },

                getRoleBadgeClass(role) {
                    if (!role) return 'bg-gray-100 text-gray-800';
                    const classMap = {
                        'admin': 'bg-red-100 text-red-800',
                        'teacher': 'bg-blue-100 text-blue-800',
                        'publisher': 'bg-green-100 text-green-800',
                        'dealer': 'bg-yellow-100 text-yellow-800'
                    };
                    return classMap[role] || 'bg-gray-100 text-gray-800';
                },

                getOrgTypeLabel(type) {
                    if (!type) return '';
                    const typeMap = {
                        'school': '学校',
                        'publisher': '出版社',
                        'dealer': '经销商'
                    };
                    return typeMap[type] || type;
                },

                // 处理排序
                handleSort(field) {
                    if (this.sortField === field) {
                        // 同一字段：升序 -> 降序 -> 取消排序
                        if (this.sortOrder === 'asc') {
                            this.sortOrder = 'desc';
                        } else if (this.sortOrder === 'desc') {
                            this.sortOrder = '';
                            this.sortField = '';
                        } else {
                            this.sortOrder = 'asc';
                        }
                    } else {
                        // 不同字段：直接设置为升序
                        this.sortField = field;
                        this.sortOrder = 'asc';
                    }

                    // 执行排序
                    this.applySorting();
                },

                // 应用排序
                applySorting() {
                    if (!this.sortField || !this.sortOrder) {
                        // 取消排序，恢复原始顺序
                        this.permissions = [...this.originalPermissions];
                    } else {
                        // 执行排序
                        this.permissions = this.sortPermissions([...this.originalPermissions], this.sortField, this.sortOrder);
                    }
                },

                // 排序函数
                sortPermissions(permissions, field, order) {
                    return permissions.sort((a, b) => {
                        let valueA, valueB;

                        // 根据字段获取比较值
                        switch (field) {
                            case 'name':
                                valueA = (a.name || a.username || '').toLowerCase();
                                valueB = (b.name || b.username || '').toLowerCase();
                                break;
                            case 'permission_name':
                                valueA = this.getPermissionLabel(a.permission_name).toLowerCase();
                                valueB = this.getPermissionLabel(b.permission_name).toLowerCase();
                                break;
                            case 'permission_value':
                                valueA = a.permission_value === 'allow' ? 1 : 0;
                                valueB = b.permission_value === 'allow' ? 1 : 0;
                                break;
                            case 'is_enabled':
                                valueA = a.is_enabled ? 1 : 0;
                                valueB = b.is_enabled ? 1 : 0;
                                break;
                            case 'created_at':
                                valueA = new Date(a.created_at || 0);
                                valueB = new Date(b.created_at || 0);
                                break;
                            default:
                                valueA = a[field] || '';
                                valueB = b[field] || '';
                        }

                        // 执行比较
                        let result = 0;
                        if (valueA < valueB) {
                            result = -1;
                        } else if (valueA > valueB) {
                            result = 1;
                        }

                        // 根据排序方向返回结果
                        return order === 'asc' ? result : -result;
                    });
                },
                




                // 显示消息
                showMessage(text, type = 'success') {
                    this.message = { show: true, text, type };
                    setTimeout(() => {
                        this.message.show = false;
                    }, 3000);
                }
            }
        }
    </script>
</body>
</html>
